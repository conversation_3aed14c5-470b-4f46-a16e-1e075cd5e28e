# Pharmacy Management System - Use Case Study

## Overview
This document outlines the key user roles in the Pharmacy Management System and their primary responsibilities and interactions with the system. The system is designed to help pharmacies manage their inventory, staff, sales, and operations efficiently.

## User Roles and Responsibilities

### 1. Pharmacy Manager (Owner)

**Role Description:**
Pharmacy Managers are the owners or designated managers of specific pharmacies. They have control over their pharmacy's staff, inventory, contracts, and business operations.

**Responsibilities:**
- Manage pharmacy staff (pharmacists)
- Create and manage staff contracts
- Manage pharmacy products
- Oversee inventory and stock levels
- Manage pharmacy sales
- View pharmacy statistics and analytics
- Configure pharmacy-specific settings

**Key Use Cases:**
- Logging into the web dashboard
- Searching and filtering products
- Viewing product details
- Editing product information
- Managing product stock levels
- Hiring pharmacists (creating contracts)
- Managing pharmacists contracts (end, renew, etc..)
- Managing work shifts and schedules
- Reviewing pharmacists status and analysis
- Reviewing sales and inventory reports
- Monitoring expired/soon-to-expire products
- Analyzing pharmacy performance metrics
- Setting and modifying product pricing

### 2. Pharmacist

**Role Description:**
Pharmacists are the front-line staff who interact with patients, manage day-to-day operations, and handle sales and inventory at the pharmacy level.

**Responsibilities:**
- Process sales and create invoices
- Manage and update product inventory
- Handle day-to-day pharmacy operations
- View product details and status
- Monitor stock levels and expiration dates

**Key Use Cases:**
- Logging into the web dashboard
- Searching and filtering products
- Viewing product details
- Managing product stock levels
- Processing sales to patients
- Identifying expired or soon-to-expire products
- Generating product reports

## System Interactions Example

### First time user who owns a pharmacy
1. User visits the website and creates new account
2. User creates a new pharmacy and becomes a "Manager"
3. User starts managing his pharmacy by creating new contracts, adding new products, etc..

### Sales Process Sequence
1. Patient requests medication
2. Pharmacist searches for the product in the system
3. Pharmacist verifies product availability and expiration (through flags set by the system)
4. Pharmacist creates a new invoice
5. Pharmacist adds products to the invoice
6. System calculates total amount
7. Pharmacist completes the sale
8. System updates inventory quantities