<?php

namespace App\Http\Requests\Product;

use Illuminate\Foundation\Http\FormRequest;

class UpdateStocksRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'stocks' => 'required|array|min:1',
            'stocks.*.productId' => 'required|uuid|exists:products,id',
            'stocks.*.quantity' => 'required|integer|min:1'
        ];
    }

    public function getStocks(): array
    {
        return $this->validated()['stocks'];
    }
}
