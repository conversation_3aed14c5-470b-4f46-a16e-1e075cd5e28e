<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Shift;
use App\Models\Contract;
use Carbon\Carbon;

class TestDataSeeder extends Seeder
{
    public function run(): void
    {
        // Create test users
        $user1 = User::create([
            'name' => 'John Pharmacist',
            'email' => '<EMAIL>',
            'password' => bcrypt('password123'),
            'email_verified_at' => now()
        ]);

        $user2 = User::create([
            'name' => 'Jane Pharmacist',
            'email' => '<EMAIL>',
            'password' => bcrypt('password123'),
            'email_verified_at' => now()
        ]);

        // Create shifts
        $morningShift = Shift::create([
            'start' => '08:00',
            'end' => '16:00',
            'days_of_week' => ['monday', 'tuesday', 'wednesday', 'thursday', 'friday']
        ]);

        $eveningShift = Shift::create([
            'start' => '16:00',
            'end' => '00:00',
            'days_of_week' => ['monday', 'tuesday', 'wednesday', 'thursday', 'friday']
        ]);

        $weekendShift = Shift::create([
            'start' => '09:00',
            'end' => '17:00',
            'days_of_week' => ['saturday', 'sunday']
        ]);

        $nightShift = Shift::create([
            'start' => '00:00',
            'end' => '08:00',
            'days_of_week' => ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']
        ]);

        // Create contracts
        // Active contract
        Contract::create([
            'user_id' => $user1->id,
            'start_date' => Carbon::now()->subMonths(2),
            'end_date' => Carbon::now()->addMonths(10),
            'monthly_salary' => 50000,
            'shift_id' => $morningShift->id,
            'pharmacy_id' => null
        ]);

        // Contract ending soon
        Contract::create([
            'user_id' => $user2->id,
            'start_date' => Carbon::now()->subMonths(6),
            'end_date' => Carbon::now()->addDays(15),
            'monthly_salary' => 45000,
            'shift_id' => $eveningShift->id,
            'pharmacy_id' => null
        ]);

        // Expired contract
        Contract::create([
            'user_id' => $user1->id,
            'start_date' => Carbon::now()->subYear(),
            'end_date' => Carbon::now()->subMonths(1),
            'monthly_salary' => 40000,
            'shift_id' => $weekendShift->id,
            'pharmacy_id' => null
        ]);

        // Future contract
        Contract::create([
            'user_id' => $user2->id,
            'start_date' => Carbon::now()->addMonth(),
            'end_date' => Carbon::now()->addYear(),
            'monthly_salary' => 55000,
            'shift_id' => $nightShift->id,
            'pharmacy_id' => null
        ]);

        echo "Test data created successfully!\n";
        echo "Users: " . User::count() . "\n";
        echo "Shifts: " . Shift::count() . "\n";
        echo "Contracts: " . Contract::count() . "\n";
    }
}
