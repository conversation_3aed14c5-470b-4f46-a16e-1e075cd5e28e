# Pharmacy Management System - Entity Relationship Diagram (ERD) Document

## Overview
This document provides a visual representation and explanation of the database structure for the Pharmacy Management System. The system is designed to manage pharmacies, users, products, inventory, sales, and related entities.

## ERD Diagram

```mermaid
erDiagram
    USERS ||--o{ CONTRACTS : has
    USERS ||--o{ PHARMACIES : owns
    CONTRACTS }|--|| SHIFTS : has
    CONTRACTS }|--|| PHARMACIES : works_at
    CONTRACTS ||--o{ INVOICES : registered_under
    PHARMACIES ||--o{ INVOICES : generates
    PHARMACIES ||--o{ PHARMACY_INVENTORY : stocks
    PRODUCTS ||--o{ PHARMACY_INVENTORY : stored_in
    PRODUCTS ||--o{ INVOICE_ITEM : sold_as
    PRODUCTS }|--|| CATEGORIES : belongs_to
    CATEGORIES ||--o{ CATEGORIES : has_subcategory
    INVOICES ||--o{ INVOICE_ITEM : contains

    users {
        UUID id PK
        VARCHAR(255) name
        VA<PERSON>HA<PERSON>(255) username
        VARCHAR(255) password
    }
    
    contracts {
        UUID id PK
        UUID user_id FK
        DATE start_date
        DATE end_date
        INTEGER monthly_salary
        UUID shift FK
        UUID pharmacy FK
    }
    
    shifts {
        UUID id PK
        TIME start
        TIME end
        VARCHAR(255) days_of_week
    }
    
    pharmacies {
        UUID id PK
        VARCHAR(255) name
        VARCHAR(255) location
        UUID owner_user_id FK
    }
    
    products {
        UUID id PK
        VARCHAR(255) name
        VARCHAR(255) active_ingredient
        TEXT description
        VARCHAR(255) shape
        DATE exp_date
        UUID category_id FK
    }
    
    invoices {
        UUID id PK
        UUID pharmacy_id FK
        UUID pharmacist_id FK
        INTEGER total_amount
        DATE issue_date
    }
    
    invoice_item {
        UUID id PK
        UUID invoice_id FK
        UUID product_id FK
        INTEGER quantity
        INTEGER unit_price
        INTEGER subtotal
    }
    
    pharmacy_inventory {
        UUID id PK
        UUID product_id FK
        UUID pharmacy_id FK
        INTEGER stock_quantity
    }
    
    categories {
        UUID id PK
        VARCHAR(255) name
        TEXT description
        UUID parent_category FK
    }
```

## Entity Descriptions

### Users
Represents all users of the system, including pharmacy managers, and pharmacists.
- **id**: Unique identifier for each user
- **name**: Full name of the user
- **username**: Unique username for login
- **password**: Encrypted password for authentication

### Contracts
Represents employment contracts between users (pharmacists) and pharmacies.
- **id**: Unique identifier for each contract
- **user_id**: Reference to the user (pharmacist)
- **start_date**: When the contract begins
- **end_date**: When the contract ends (can be null for ongoing contracts)
- **monthly_salary**: Salary amount
- **shift**: Reference to the work shift
- **pharmacy**: Reference to the pharmacy where the pharmacist works

### Shifts
Defines work schedules for pharmacists.
- **id**: Unique identifier for each shift
- **start**: Start time of the shift
- **end**: End time of the shift
- **days_of_week**: Days when the shift applies

### Pharmacies
Represents individual pharmacy locations.
- **id**: Unique identifier for each pharmacy
- **name**: Name of the pharmacy
- **location**: Physical address or location
- **owner_user_id**: Reference to the user who owns/manages the pharmacy

### Products
Represents pharmaceutical products available in the system.
- **id**: Unique identifier for each product
- **name**: Product name
- **active_ingredient**: Main active ingredient
- **description**: Detailed product description
- **shape**: Physical form (serum, syrup, capsules, films)
- **exp_date**: Expiration date
- **category_id**: Reference to the product category

### Invoices
Represents sales transactions.
- **id**: Unique identifier for each invoice
- **pharmacy_id**: Reference to the pharmacy where the sale occurred
- **pharmacist_id**: Reference to the contract of the pharmacist who processed the sale
- **total_amount**: Total sale amount
- **issue_date**: Date when the invoice was created

### Invoice_Item
Represents individual line items in an invoice.
- **id**: Unique identifier for each item
- **invoice_id**: Reference to the parent invoice
- **product_id**: Reference to the product sold
- **quantity**: Number of units sold
- **unit_price**: Price per unit
- **subtotal**: Total price for this line item

### Pharmacy_Inventory
Tracks product stock levels at each pharmacy.
- **id**: Unique identifier for each inventory record
- **product_id**: Reference to the product
- **pharmacy_id**: Reference to the pharmacy
- **stock_quantity**: Current quantity in stock

### Categories
Organizes products into hierarchical categories.
- **id**: Unique identifier for each category
- **name**: Category name
- **description**: Category description
- **parent_category**: Reference to parent category (null for top-level categories)

## Key Relationships

1. **Users to Pharmacies**: One user (manager) can own multiple pharmacies
2. **Users to Contracts**: One user can have multiple contracts (work at different pharmacies)
3. **Pharmacies to Inventory**: One pharmacy has multiple inventory items
4. **Products to Categories**: Each product belongs to one category
5. **Categories to Categories**: Categories can have subcategories (hierarchical)
6. **Invoices to Items**: One invoice contains multiple line items
7. **Contracts to Invoices**: Pharmacists (via their contracts) issue invoices
8. **Products to Inventories**: Products are tracked in pharmacy inventory