<?php

namespace App\Http\Requests\Contract;

use Illuminate\Foundation\Http\FormRequest;

class RenewContractRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'endDate' => 'required|date|after:today',
            'monthlySalary' => 'nullable|integer|min:1',
            'shiftId' => 'nullable|uuid|exists:shifts,id',
            'pharmacyId' => 'nullable|uuid',
        ];
    }

    public function validatedData(): array
    {
        $validated = $this->validated();

        $data = [];
        foreach ($validated as $key => $value) {
            $dbKey = match ($key) {
                'endDate' => 'end_date',
                'monthlySalary' => 'monthly_salary',
                'shiftId' => 'shift_id',
                'pharmacyId' => 'pharmacy_id',
                default => $key
            };
            $data[$dbKey] = $value;
        }

        return $data;
    }
}
