{"name": "mobile", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "DARK_MODE=media expo start --android", "ios": "DARK_MODE=media expo start --ios", "web": "DARK_MODE=media expo start --web", "test": "jest --watchAll", "lint": "expo lint"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@expo/vector-icons": "^14.0.2", "@gluestack-ui/button": "^1.0.14", "@gluestack-ui/form-control": "^0.1.19", "@gluestack-ui/icon": "^0.1.27", "@gluestack-ui/input": "^0.1.38", "@gluestack-ui/nativewind-utils": "^1.0.26", "@gluestack-ui/overlay": "^0.1.22", "@gluestack-ui/toast": "^1.0.9", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "babel-plugin-module-resolver": "^5.0.2", "dotenv": "^16.5.0", "expo": "~52.0.46", "expo-blur": "~14.0.3", "expo-constants": "~17.0.8", "expo-font": "~13.0.4", "expo-haptics": "~14.0.1", "expo-linking": "~7.0.5", "expo-router": "~4.0.20", "expo-secure-store": "~14.0.1", "expo-splash-screen": "~0.29.24", "expo-status-bar": "~2.0.1", "expo-symbols": "~0.2.2", "expo-system-ui": "~4.0.9", "expo-web-browser": "~14.0.2", "formik": "^2.4.6", "mobile": "file:", "nativewind": "^4.1.23", "react": "18.3.1", "react-dom": "18.3.1", "react-hook-form": "^7.56.1", "react-native": "0.76.9", "react-native-css-interop": "^0.1.22", "react-native-gesture-handler": "~2.20.2", "react-native-keychain": "^10.0.0", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "~4.4.0", "react-native-svg": "^15.2.0", "react-native-web": "~0.19.13", "react-native-webview": "13.12.5", "tailwindcss": "^3.4.17", "yup": "^1.6.1", "zod": "^3.24.3"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/jest": "^29.5.12", "@types/react": "~18.3.12", "@types/react-test-renderer": "^18.3.0", "jest": "^29.2.1", "jest-expo": "~52.0.6", "jscodeshift": "^0.15.2", "react-test-renderer": "18.3.1", "typescript": "^5.3.3"}, "private": true}