# Products API Documentation

## Authentication

All endpoints require authentication. Include the authentication token in the request header:

```
Authorization: Bear<PERSON> {your_token}
```

## Response Format

All responses are in JSON format with camelCase property names. Paginated responses include pagination metadata.

## Endpoints

### 1. List All Products

Retrieves a paginated list of products with optional filtering.

- **URL**: `/api/products`
- **Method**: `GET`
- **Auth Required**: Yes

#### Query Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| page | integer | No | Page number (default: 1) |
| perPage | integer | No | Items per page (default: 15) |
| search | string | No | Search term for product name or active ingredient |
| categoryId | uuid | No | Filter products by category ID |
| expirationStatus | string | No | Filter by expiration status: 'expired' or 'expiring_soon' |
| months | integer | No | Number of months for expiring soon filter (default: 3) |
| sortByExpiration | boolean | No | Sort products by expiration date (default: false) |

#### Success Response

```json
{
  "data": [
    {
      "id": "uuid-string",
      "name": "Product Name",
      "activeIngredient": "Active Ingredient",
      "description": "Product description",
      "shape": "Tablet",
      "expDate": "2023-12-31",
      "categoryId": "category-uuid",
      "quantity": 100,
      "createdAt": "2023-01-01T00:00:00.000000Z",
      "updatedAt": "2023-01-01T00:00:00.000000Z"
    },
    // More products...
  ],
  "links": {
    "first": "http://example.com/api/products?page=1",
    "last": "http://example.com/api/products?page=5",
    "prev": null,
    "next": "http://example.com/api/products?page=2"
  },
  "meta": {
    "current_page": 1,
    "from": 1,
    "last_page": 5,
    "path": "http://example.com/api/products",
    "per_page": 15,
    "to": 15,
    "total": 75
  },
  "page": 1,
  "perPage": 15
}
```

### 2. Get Product by ID

Retrieves a specific product by its ID.

- **URL**: `/api/products/{id}`
- **Method**: `GET`
- **Auth Required**: Yes

#### URL Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| id | uuid | Yes | Product ID |

#### Success Response

```json
{
  "data": {
    "id": "uuid-string",
    "name": "Product Name",
    "activeIngredient": "Active Ingredient",
    "description": "Product description",
    "shape": "Tablet",
    "expDate": "2023-12-31",
    "categoryId": "category-uuid",
    "quantity": 100,
    "createdAt": "2023-01-01T00:00:00.000000Z",
    "updatedAt": "2023-01-01T00:00:00.000000Z"
  }
}
```

#### Error Response

```json
{
  "message": "product not found"
}
```

### 3. Create Product

Creates a new product.

- **URL**: `/api/products`
- **Method**: `POST`
- **Auth Required**: Yes
- **Content-Type**: `application/json`

#### Request Body

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| name | string | Yes | Product name (max: 255 chars) |
| activeIngredient | string | Yes | Active ingredient (max: 255 chars) |
| description | string | No | Product description |
| shape | string | Yes | Product shape (max: 255 chars) |
| expDate | date | Yes | Expiration date (must be after today) |
| categoryId | uuid | Yes | Category ID (must exist in categories table) |
| quantity | integer | No | Initial stock quantity (min: 0) |

#### Success Response

```json
{
  "data": {
    "id": "uuid-string",
    "name": "Product Name",
    "activeIngredient": "Active Ingredient",
    "description": "Product description",
    "shape": "Tablet",
    "expDate": "2023-12-31",
    "categoryId": "category-uuid",
    "quantity": 100,
    "createdAt": "2023-01-01T00:00:00.000000Z",
    "updatedAt": "2023-01-01T00:00:00.000000Z"
  },
  "message": "product created successfully"
}
```

### 4. Update Product

Updates an existing product.

- **URL**: `/api/products/{id}`
- **Method**: `PUT`
- **Auth Required**: Yes
- **Content-Type**: `application/json`

#### URL Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| id | uuid | Yes | Product ID |

#### Request Body

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| name | string | No | Product name (max: 255 chars) |
| activeIngredient | string | No | Active ingredient (max: 255 chars) |
| description | string | No | Product description |
| shape | string | No | Product shape (max: 255 chars) |
| expDate | date | No | Expiration date |
| categoryId | uuid | No | Category ID (must exist in categories table) |
| quantity | integer | No | Stock quantity (min: 0) |

#### Success Response

```json
{
  "data": {
    "id": "uuid-string",
    "name": "Updated Product Name",
    "activeIngredient": "Updated Active Ingredient",
    "description": "Updated product description",
    "shape": "Capsule",
    "expDate": "2024-12-31",
    "categoryId": "category-uuid",
    "quantity": 150,
    "createdAt": "2023-01-01T00:00:00.000000Z",
    "updatedAt": "2023-01-02T00:00:00.000000Z"
  },
  "message": "product updated successfully"
}
```

#### Error Response

```json
{
  "message": "product not found"
}
```

### 5. Delete Product

Deletes a product.

- **URL**: `/api/products/{id}`
- **Method**: `DELETE`
- **Auth Required**: Yes

#### URL Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| id | uuid | Yes | Product ID |

#### Success Response

- Status Code: 204 No Content

#### Error Response

```json
{
  "message": "product not found"
}
```

### 6. Get Expired Products

Retrieves a paginated list of expired products.

- **URL**: `/api/products/expired`
- **Method**: `GET`
- **Auth Required**: Yes

#### Query Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| page | integer | No | Page number (default: 1) |
| perPage | integer | No | Items per page (default: 15) |

#### Success Response

Similar to the List All Products response, but only includes expired products.

### 7. Get Expiring Soon Products

Retrieves a paginated list of products that will expire soon.

- **URL**: `/api/products/expiring-soon`
- **Method**: `GET`
- **Auth Required**: Yes

#### Query Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| page | integer | No | Page number (default: 1) |
| perPage | integer | No | Items per page (default: 15) |
| months | integer | No | Number of months to consider for "expiring soon" (default: 3) |

#### Success Response

Similar to the List All Products response, but only includes products expiring within the specified months.

### 8. Search Products

Searches for products by name or active ingredient.

- **URL**: `/api/products/search`
- **Method**: `POST`
- **Auth Required**: Yes
- **Content-Type**: `application/json`

#### Request Body

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| term | string | Yes | Search term (min: 2 chars) |

#### Query Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| page | integer | No | Page number (default: 1) |
| perPage | integer | No | Items per page (default: 15) |

#### Success Response

Similar to the List All Products response, but only includes products matching the search term.

### 9. Update Product Stocks

Updates the stock quantities for multiple products in a single request.

- **URL**: `/api/products/stocks/update`
- **Method**: `POST`
- **Auth Required**: Yes
- **Content-Type**: `application/json`

#### Request Body

```json
{
  "stocks": [
    {
      "productId": "uuid-string-1",
      "quantity": 10
    },
    {
      "productId": "uuid-string-2",
      "quantity": 20
    }
  ]
}
```

#### Success Response

```json
{
  "message": "products stocks updated successfully",
  "data": {
    "success": [
      {
        "productId": "uuid-string-1",
        "productName": "Product 1",
        "oldQuantity": 90,
        "newQuantity": 100
      },
      {
        "productId": "uuid-string-2",
        "productName": "Product 2",
        "oldQuantity": 30,
        "newQuantity": 50
      }
    ],
    "failed": []
  }
}
```

## Error Handling

All endpoints may return the following error responses:

- **401 Unauthorized**: If the authentication token is missing or invalid
- **422 Unprocessable Entity**: If the request data fails validation
- **404 Not Found**: If the requested resource is not found
- **500 Internal Server Error**: If there's a server error

## Pagination

Paginated responses include the following metadata:

- `links`: Contains URLs for first, last, previous, and next pages
- `meta`: Contains pagination information like current page, total pages, etc.
- `page`: Current page number
- `perPage`: Number of items per page

# Contracts and Shifts API Documentation

The system implements a contract management system where:
- **Users** can have multiple contracts
- **Contracts** are linked to specific shifts and users
- **Shifts** define working hours and days of the week
- Contracts can be created, updated, ended, and renewed

## Authentication
All endpoints require authentication using Bearer token in the Authorization header.

## Shifts API

### GET /api/shifts
Get all shifts with pagination and filtering.

**Query Parameters:**
- `page` (int) - Page number (default: 1)
- `perPage` (int) - Items per page (default: 15)
- `search` (string) - Search in start/end times
- `days` (string) - Comma-separated list of days to filter by
- `startTime` (string) - Filter by minimum start time
- `endTime` (string) - Filter by maximum end time

### POST /api/shifts
Create a new shift.

**Request Body:**
```json
{
  "start": "09:00",
  "end": "17:00",
  "daysOfWeek": ["monday", "tuesday", "wednesday", "thursday", "friday"]
}
```

### GET /api/shifts/{id}
Get a specific shift by ID.

### PUT /api/shifts/{id}
Update a specific shift.

### DELETE /api/shifts/{id}
Delete a specific shift (only if not used by any contracts).

### GET /api/shifts/by-days?days=monday,tuesday
Get shifts filtered by specific days.

### GET /api/shifts/available
Get shifts not currently assigned to any active contracts.

## Contracts API

### GET /api/contracts
Get all contracts with pagination and filtering.

**Query Parameters:**
- `page` (int) - Page number
- `perPage` (int) - Items per page
- `search` (string) - Search in user name/email
- `userId` (UUID) - Filter by user
- `shiftId` (UUID) - Filter by shift
- `status` (string) - Filter by status: 'active', 'expired', 'future', 'ending_soon'
- `startDateFrom`, `startDateTo` - Filter by start date range
- `endDateFrom`, `endDateTo` - Filter by end date range
- `salaryMin`, `salaryMax` - Filter by salary range
- `endingSoonDays` (int) - Days threshold for ending soon filter

### POST /api/contracts
Create a new contract.

**Request Body:**
```json
{
  "userId": "uuid",
  "startDate": "2025-01-01",
  "endDate": "2025-12-31",
  "monthlySalary": 50000,
  "shiftId": "uuid",
  "pharmacyId": "uuid"
}
```

#### GET /api/contracts/{id}
Get a specific contract by ID.

#### PUT /api/contracts/{id}
Update a specific contract.

#### DELETE /api/contracts/{id}
Delete a specific contract.

#### PATCH /api/contracts/{id}/end
End a contract early.

**Request Body:**
```json
{
  "endDate": "2025-06-30" // optional, defaults to today
}
```

#### POST /api/contracts/{id}/renew
Renew a contract (creates a new contract starting after the current one ends).

**Request Body:**
```json
{
  "endDate": "2026-12-31",
  "monthlySalary": 55000, // optional, defaults to current salary
  "shiftId": "uuid", // optional, defaults to current shift
  "pharmacyId": "uuid"
}
```

#### GET /api/contracts/active
Get all currently active contracts.

#### GET /api/contracts/expired
Get all expired contracts.

#### GET /api/contracts/ending-soon?days=30
Get contracts ending within specified days (default: 30).

#### GET /api/contracts/user/{userId}
Get all contracts for a specific user.

## Response Format

All responses follow the established camelCase format using Resource classes:

### Shift Resource
```json
{
  "id": "uuid",
  "start": "09:00",
  "end": "17:00",
  "daysOfWeek": ["monday", "tuesday", "wednesday", "thursday", "friday"],
  "createdAt": "2025-01-01T00:00:00.000000Z",
  "updatedAt": "2025-01-01T00:00:00.000000Z",
  "contractsCount": 5
}
```

### Contract Resource
```json
{
  "id": "uuid",
  "userId": "uuid",
  "startDate": "2025-01-01",
  "endDate": "2025-12-31",
  "monthlySalary": 50000,
  "shiftId": "uuid",
  "pharmacyId": null,
  "createdAt": "2025-01-01T00:00:00.000000Z",
  "updatedAt": "2025-01-01T00:00:00.000000Z",
  "isActive": true,
  "isExpired": false,
  "isFuture": false,
  "durationInDays": 365,
  "durationInMonths": 12,
  "remainingDays": 300,
  "user": { /* UserResource */ },
  "shift": { /* ShiftResource */ }
}
```

## Validation Rules

### Shifts
- `start`: Required, valid time format (HH:MM)
- `end`: Required, valid time format (HH:MM), must be after start time
- `daysOfWeek`: Required array with at least one valid day

### Contracts
- `userId`: Required, valid UUID, must exist in users table
- `startDate`: Required, valid date, must be today or future (for new contracts)
- `endDate`: Required, valid date, must be after start date
- `monthlySalary`: Required, integer, minimum 1
- `shiftId`: Required, valid UUID, must exist in shifts table
- `pharmacyId`: Optional, valid UUID (validation for existence will be added when pharmacies are implemented)

## Future Enhancements

When pharmacies are implemented:
1. Add foreign key constraint for `pharmacy_id` in contracts table
2. Update validation rules to check pharmacy existence
3. Add pharmacy relationship to Contract resource
4. Update contract filtering to include pharmacy-based filters
